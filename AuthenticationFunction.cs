using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.Graph;
using Microsoft.Graph.Models;
using System.Net;
using System.Text.Json;
using PasswordHistoryValidator.Services;
using PasswordHistoryValidator.Shared;
using System.Collections.Generic;
using System.Linq;

namespace PasswordHistoryValidator;

/// <summary>
/// auth ops
/// </summary>
public class AuthenticationFunction : BaseFunctionService
{
    private readonly ILogger<AuthenticationFunction> _logger;
    private readonly IPasswordHistoryService _passwordHistoryService;
    private readonly GraphServiceClient _graphServiceClient;
    private readonly EntraOptions _entraOptions;
    private readonly IHttpClientFactory _httpClientFactory;

    public AuthenticationFunction(
        ILogger<AuthenticationFunction> logger,
        IPasswordHistoryService passwordHistoryService,
        GraphServiceClient graphServiceClient,
        IOptions<EntraOptions> entraOptions,
        IHttpClientFactory httpClientFactory,
        JsonSerializerOptions jsonOptions) : base(jsonOptions)
    {
        _logger = logger;
        _passwordHistoryService = passwordHistoryService;
        _graphServiceClient = graphServiceClient;
        _entraOptions = entraOptions.Value;
        _httpClientFactory = httpClientFactory;
    }

    /// <summary>
    /// Main entry point for authentication operations
    /// Supports: login, validate-credentials
    /// </summary>
    [Function("AuthenticationService")]
    public async Task<HttpResponseData> Run(
        [HttpTrigger(AuthorizationLevel.Function, "post", "options")] HttpRequestData req,
        CancellationToken cancellationToken)
    {
        var correlationId = GenerateCorrelationId();

        // Handle CORS preflight requests
        if (req.Method.Equals("OPTIONS", StringComparison.OrdinalIgnoreCase))
        {
            return CreateCorsResponse(req);
        }

        try
        {
            var operation = req.Query["operation"];
            if (string.IsNullOrEmpty(operation))
            {
                return await CreateErrorResponse(req, "Operation parameter required", correlationId);
            }

            // Route to appropriate handler based on operation
            return operation.ToLower() switch
            {
                "login" => await HandleUserLogin(req, correlationId, cancellationToken),
                "validate-credentials" => await HandleCredentialValidation(req, correlationId, cancellationToken),
                _ => await CreateErrorResponse(req, $"Invalid operation: {operation}", correlationId)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Auth service error [CorrelationId: {CorrelationId}]", correlationId);
            return await CreateErrorResponse(req, "Service error", correlationId);
        }
    }

    private async Task<HttpResponseData> HandleUserLogin(HttpRequestData req, string correlationId, CancellationToken cancellationToken)
    {
        var data = await JsonSerializer.DeserializeAsync<AuthRequest>(req.Body, JsonOptions, cancellationToken);
        if (data == null || string.IsNullOrWhiteSpace(data.Email) || string.IsNullOrWhiteSpace(data.Password))
            return await CreateErrorResponse(req, "Email and password required", correlationId);

        var applicationName = data.ApplicationName ?? "Default Application";

        // Find user in Entra External ID by email and application context
        var emailEsc = ODataHelpers.EscapeString(data.Email);
        var appEsc = ODataHelpers.EscapeString(applicationName);
        var users = await _graphServiceClient.Users
            .GetAsync(requestConfiguration =>
            {
                requestConfiguration.QueryParameters.Filter =
                    $"(mail eq '{emailEsc}' or userPrincipalName eq '{emailEsc}' or proxyAddresses/any(c:c eq 'SMTP:{emailEsc}')) and department eq '{appEsc}'";
                requestConfiguration.QueryParameters.Select = new[] { "id", "userPrincipalName", "identities", "mail", "displayName", "department" };
                requestConfiguration.QueryParameters.Top = 1;
                requestConfiguration.QueryParameters.Orderby = new[] { "createdDateTime desc" };
            }, cancellationToken);

        if (users?.Value == null || users.Value.Count == 0)
        {
            _logger.LogWarning("Login failed for {Email} [CorrelationId: {CorrelationId}]", data.Email, correlationId);
            return await CreateErrorResponse(req, "Invalid email or password", correlationId);
        }

        // Handle multiple users (select most recent)
        User user;
        if (users.Value.Count > 1)
        {
            _logger.LogWarning("Multiple users found for {Email}, selecting most recent [CorrelationId: {CorrelationId}]", data.Email, correlationId);
            user = users.Value.OrderBy(u => u.Id).Last();
        }
        else
        {
            user = users.Value.First();
        }

        // Validate credentials using custom password history
        var usernameForAuth = user.UserPrincipalName ?? data.Email;
        var isValidCredentials = await ValidateUserCredentials(usernameForAuth, data.Password, data.ApplicationName ?? "Default Application", data.Email, cancellationToken);

        // Fallback to email if UPN validation fails
        if (!isValidCredentials && !string.Equals(usernameForAuth, data.Email, StringComparison.OrdinalIgnoreCase))
        {
            isValidCredentials = await ValidateUserCredentials(data.Email, data.Password, data.ApplicationName ?? "Default Application", data.Email, cancellationToken);
        }

        if (!isValidCredentials)
            return await CreateErrorResponse(req, "Invalid email or password", correlationId);

        return await CreateJsonResponse(req, new
        {
            success = true,
            message = "Login successful",
            userId = user.Id,
            email = user.Mail,
            displayName = user.DisplayName
        }, HttpStatusCode.OK, correlationId);
    }

    private async Task<bool> ValidateUserCredentials(string username, string password, string applicationName, string email, CancellationToken cancellationToken)
    {
        // First check against stored password history
        var historyResult = await _passwordHistoryService.GetPasswordHistoryAsync(
            applicationName ?? "Default Application",
            email,
            cancellationToken);

        if (historyResult.IsSuccess && historyResult.Value != null && historyResult.Value.Count > 0)
        {
            var mostRecentHash = historyResult.Value.First();
            if (BCrypt.Net.BCrypt.Verify(password, mostRecentHash))
            {
                return true;
            }
        }

        // Fallback to ROPC validation for external domains
        return await TryROPCValidation(username, password, cancellationToken);
    }

    private async Task<bool> TryROPCValidation(string email, string password, CancellationToken cancellationToken)
    {
        try
        {
            var tokenEndpoint = $"https://login.microsoftonline.com/{_entraOptions.TenantId}/oauth2/v2.0/token";

            var formData = new List<KeyValuePair<string, string>>
            {
                new("grant_type", "password"),
                new("client_id", _entraOptions.ClientId),
                new("client_secret", _entraOptions.ClientSecret),
                new("scope", "https://graph.microsoft.com/.default"),
                new("username", email),
                new("password", password)
            };

            using var httpClient = _httpClientFactory.CreateClient();
            using var content = new FormUrlEncodedContent(formData);

            var response = await httpClient.PostAsync(tokenEndpoint, content, cancellationToken);
            return response.IsSuccessStatusCode;
        }
        catch
        {
            return false;
        }
    }

    private async Task<HttpResponseData> HandleCredentialValidation(HttpRequestData req, string correlationId, CancellationToken cancellationToken)
    {
        var data = await JsonSerializer.DeserializeAsync<AuthRequest>(req.Body, JsonOptions, cancellationToken);
        if (data == null || string.IsNullOrWhiteSpace(data.Email) || string.IsNullOrWhiteSpace(data.Password))
            return await CreateErrorResponse(req, "Email and password required", correlationId);

        var applicationName = data.ApplicationName ?? "Default Application";

        // Find user by email and application context
        var emailEsc = ODataHelpers.EscapeString(data.Email);
        var appEsc = ODataHelpers.EscapeString(applicationName);
        var users = await _graphServiceClient.Users
            .GetAsync(requestConfiguration =>
            {
                requestConfiguration.QueryParameters.Filter =
                    $"(mail eq '{emailEsc}' or userPrincipalName eq '{emailEsc}' or proxyAddresses/any(c:c eq 'SMTP:{emailEsc}')) and department eq '{appEsc}'";
                requestConfiguration.QueryParameters.Select = new[] { "id", "userPrincipalName", "mail", "displayName", "department" };
                requestConfiguration.QueryParameters.Top = 1;
            }, cancellationToken);

        if (users?.Value == null || users.Value.Count == 0)
            return await CreateErrorResponse(req, "Invalid email or password", correlationId);

        var user = users.Value.First();
        var usernameForAuth = user.UserPrincipalName ?? data.Email;

        // Validate credentials using password history
        var isValidCredentials = await ValidateUserCredentials(usernameForAuth, data.Password, data.ApplicationName ?? "Default Application", data.Email, cancellationToken);

        if (isValidCredentials)
        {
            return await CreateJsonResponse(req, new
            {
                success = true,
                message = "Credentials validated successfully",
                email = user.Mail,
                displayName = user.DisplayName
            }, HttpStatusCode.OK, correlationId);
        }
        else
        {
            return await CreateErrorResponse(req, "Invalid email or password", correlationId);
        }
    }
}
