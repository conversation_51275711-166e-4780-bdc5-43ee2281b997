using System.Security.Cryptography;

namespace PasswordHistoryValidator.Shared;

/// <summary>
/// token utils
/// </summary>
public static class TokenHelper
{
    /// <summary>
    /// six digit code
    /// </summary>
    public static string GenerateVerificationCode()
    {
        using var rng = RandomNumberGenerator.Create();
        var bytes = new byte[4];
        rng.GetBytes(bytes);
        var randomNumber = BitConverter.ToUInt32(bytes, 0);
        var code = (randomNumber % 900000) + 100000; // 6 digit
        return code.ToString();
    }
}