namespace PasswordHistoryValidator.Shared;


/// sendgrid opts

public class SendGridOptions
{
    public const string SectionName = "SendGrid";

    public string ApiKey { get; set; } = string.Empty;
    public string FromEmail { get; set; } = string.Empty;
    public string PasswordResetTemplateId { get; set; } = string.Empty;
    public string PasswordChangedTemplateId { get; set; } = string.Empty;
    public string UserInvitationTemplateId { get; set; } = string.Empty;
    public string AccountCreatedTemplateId { get; set; } = string.Empty;
}


/// entra opts

public class EntraOptions
{
    public const string SectionName = "EntraExternalID";

    public string TenantId { get; set; } = string.Empty;
    public string ClientId { get; set; } = string.Empty;
    public string ClientSecret { get; set; } = string.Empty;

    public string DefaultDomain { get; set; } = "yourtenant.onmicrosoft.com";
}


/// reset opts

public class PasswordResetOptions
{
    public const string SectionName = "PasswordReset";

    public string BaseUrl { get; set; } = string.Empty;
}

/// register opts

public class AccountRegistrationOptions
{
    public const string SectionName = "AccountRegistration";

    public string BaseUrl { get; set; } = string.Empty;
}

/// ratelimit opts

public class RateLimitOptions
{
    public const string SectionName = "RateLimit";

    public int MaxRequestsPerMinute { get; set; } = 60;
}

public class StorageOptions
{
    public const string SectionName = "Storage";

    public string ConnectionString { get; set; } = string.Empty;
}

public class InvitationOptions
{
    public const string SectionName = "Invitation";

    public int TokenExpirationDays { get; set; } = 45;
}
