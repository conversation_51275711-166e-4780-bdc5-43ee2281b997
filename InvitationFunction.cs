using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Logging;
using System.Net;
using System.Text.Json;
using System.ComponentModel.DataAnnotations;
using PasswordHistoryValidator.Services;
using PasswordHistoryValidator.Shared;

namespace PasswordHistoryValidator;

/// <summary>
/// invite ops
/// </summary>
public partial class InvitationFunction : BaseFunctionService
{
    private readonly ILogger<InvitationFunction> _logger;
    private readonly InvitationTokenManager _invitationTokenManager;
    private readonly IEmailService _emailService;
    private readonly RateLimitService _rateLimitService;

    public InvitationFunction(
        ILogger<InvitationFunction> logger,
        InvitationTokenManager invitationTokenManager,
        IEmailService emailService,
        RateLimitService rateLimitService,
        JsonSerializerOptions jsonOptions) : base(jsonOptions)
    {
        _logger = logger;
        _invitationTokenManager = invitationTokenManager;
        _emailService = emailService;
        _rateLimitService = rateLimitService;
    }

    /// <summary>
    /// Main entry point for invitation operations
    /// Supports: send-invitation, validate-invitation
    /// </summary>
    [Function("InvitationService")]
    public async Task<HttpResponseData> Run(
        [HttpTrigger(AuthorizationLevel.Function, "post", "options")] HttpRequestData req,
        CancellationToken cancellationToken)
    {
        var correlationId = GenerateCorrelationId();

        // Handle CORS preflight requests
        if (req.Method.Equals("OPTIONS", StringComparison.OrdinalIgnoreCase))
        {
            return CreateCorsResponse(req);
        }

        try
        {
            var operation = req.Query["operation"];
            if (string.IsNullOrEmpty(operation))
            {
                return await CreateErrorResponse(req, "Operation parameter required", correlationId);
            }

            return operation.ToLower() switch
            {
                "invite-user" => await HandleInviteUser(req, correlationId, cancellationToken),
                "validate-token" => await HandleValidateToken(req, correlationId, cancellationToken),
                _ => await CreateErrorResponse(req, $"Invalid operation: {operation}", correlationId)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Invitation service error [CorrelationId: {CorrelationId}]", correlationId);
            return await CreateErrorResponse(req, "Service error", correlationId);
        }
    }

    private async Task<HttpResponseData> HandleInviteUser(HttpRequestData req, string correlationId, CancellationToken cancellationToken)
    {
        var data = await JsonSerializer.DeserializeAsync<InvitationRequest>(req.Body, JsonOptions, cancellationToken);

        if (data == null)
            return await CreateErrorResponse(req, "Invalid request data", correlationId);

        // Validate request data
        var validationResults = new List<ValidationResult>();
        var validationContext = new ValidationContext(data);
        if (!Validator.TryValidateObject(data, validationContext, validationResults, true))
        {
            var errors = string.Join(", ", validationResults.Select(r => r.ErrorMessage));
            return await CreateErrorResponse(req, $"Validation failed: {errors}", correlationId);
        }

        // Rate limiting
        var clientId = BaseFunctionService.GetClientIdentifier(req);
        var rateLimitInfo = await _rateLimitService.CheckRateLimitAsync(clientId, "invite-user", cancellationToken);
        if (!rateLimitInfo.IsAllowed)
        {
            return await CreateJsonResponse(req, new
            {
                success = false,
                message = "Rate limit exceeded. Please try again later.",
                errorCode = "RateLimitExceeded",
                retryAfter = rateLimitInfo.WindowResetTime
            }, HttpStatusCode.TooManyRequests, correlationId);
        }

        // Generate and store invitation token
        var token = _invitationTokenManager.GenerateInvitationToken();
        var verificationCode = await _invitationTokenManager.StoreInvitationToken(data.ApplicationName, data.Email, token);

        // Send invitation email in background (non-critical operation)
        _ = Task.Run(async () => await SendInvitationEmailAsync(data, token, verificationCode, correlationId));

        return await CreateJsonResponse(req, new
        {
            success = true,
            message = "Invitation sent successfully",
            correlationId = correlationId
        }, HttpStatusCode.OK, correlationId);
    }

    private async Task<HttpResponseData> HandleValidateToken(HttpRequestData req, string correlationId, CancellationToken cancellationToken)
    {
        var data = await JsonSerializer.DeserializeAsync<TokenValidationRequest>(req.Body, JsonOptions, cancellationToken);

        if (data == null || string.IsNullOrEmpty(data.Token))
            return await CreateErrorResponse(req, "Token is required", correlationId);

        // Rate limiting
        var clientId = BaseFunctionService.GetClientIdentifier(req);
        var rateLimitInfo = await _rateLimitService.CheckRateLimitAsync(clientId, "validate-token", cancellationToken);
        if (!rateLimitInfo.IsAllowed)
        {
            return await CreateJsonResponse(req, new
            {
                success = false,
                message = "Rate limit exceeded. Please try again later.",
                errorCode = "RateLimitExceeded",
                retryAfter = rateLimitInfo.WindowResetTime
            }, HttpStatusCode.TooManyRequests, correlationId);
        }

        // Validate invitation token
        var (isValid, tokenData, errorMessage) = await _invitationTokenManager.ValidateInvitationTokenOnly(data.Token);

        if (!isValid || tokenData == null)
        {
            _logger.LogWarning("Token validation failed: {ErrorMessage} [CorrelationId: {CorrelationId}]",
                errorMessage, correlationId);
            return await CreateJsonResponse(req, new
            {
                success = false,
                message = errorMessage ?? "Invalid or expired invitation token"
            }, HttpStatusCode.Unauthorized, correlationId);
        }

        _logger.LogInformation("Token validation successful for {Email} [CorrelationId: {CorrelationId}]",
            tokenData.Email, correlationId);

        return await CreateJsonResponse(req, new
        {
            success = true,
            message = "Token is valid",
            email = tokenData.Email,
            applicationId = tokenData.ApplicationId,
            expiresUtc = tokenData.ExpiresUtc
        }, HttpStatusCode.OK, correlationId);
    }

    private async Task SendInvitationEmailAsync(InvitationRequest data, string token, string verificationCode, string correlationId)
    {
        try
        {
            var emailSent = await _emailService.SendUserInvitationEmailAsync(
                data.Email, token, verificationCode, data.ApplicationName,
                data.FirstName ?? "", data.LastName ?? "", correlationId);

            if (emailSent)
            {
                _logger.LogInformation("Invitation email sent successfully to {Email} [CorrelationId: {CorrelationId}]",
                    data.Email, correlationId);
            }
            else
            {
                _logger.LogError("Failed to send invitation email to {Email} [CorrelationId: {CorrelationId}]",
                    data.Email, correlationId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending invitation email to {Email} [CorrelationId: {CorrelationId}]",
                data.Email, correlationId);
        }
    }

}
