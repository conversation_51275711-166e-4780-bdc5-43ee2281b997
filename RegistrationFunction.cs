using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Configuration;
using Microsoft.Graph;
using Microsoft.Graph.Models;
using System.Net;
using System.Text.Json;
using System.ComponentModel.DataAnnotations;
using PasswordHistoryValidator.Services;
using PasswordHistoryValidator.Shared;
using Azure.Identity;
using System.Collections.Generic;
using System.Linq;

namespace PasswordHistoryValidator;

/// <summary>
/// register ops
/// </summary>
public class RegistrationFunction : BaseFunctionService
{
    private readonly ILogger<RegistrationFunction> _logger;
    private readonly IPasswordHistoryService _passwordHistoryService;
    private readonly RateLimitService _rateLimitService;
    private readonly GraphServiceClient _graphServiceClient;
    private readonly IConfiguration _configuration;
    private readonly EntraOptions _entraOptions;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly InvitationTokenManager _invitationTokenManager;
    private readonly IEmailService _emailService;

    public RegistrationFunction(
        ILogger<RegistrationFunction> logger,
        IPasswordHistoryService passwordHistoryService,
        RateLimitService rateLimitService,
        GraphServiceClient graphServiceClient,
        IConfiguration configuration,
        IOptions<EntraOptions> entraOptions,
        IHttpClientFactory httpClientFactory,
        InvitationTokenManager invitationTokenManager,
        IEmailService emailService,
        JsonSerializerOptions jsonOptions) : base(jsonOptions)
    {
        _logger = logger;
        _passwordHistoryService = passwordHistoryService;
        _rateLimitService = rateLimitService;
        _graphServiceClient = graphServiceClient;
        _configuration = configuration;
        _entraOptions = entraOptions.Value;
        _httpClientFactory = httpClientFactory;
        _invitationTokenManager = invitationTokenManager;
        _emailService = emailService;
    }

    [Function("RegistrationService")]
    public async Task<HttpResponseData> Run(
        [HttpTrigger(AuthorizationLevel.Function, "post", "options")] HttpRequestData req,
        CancellationToken cancellationToken)
    {
        var correlationId = GenerateCorrelationId();

        if (req.Method.Equals("OPTIONS", StringComparison.OrdinalIgnoreCase))
        {
            return CreateCorsResponse(req);
        }

        try
        {
            var operation = req.Query["operation"];
            if (string.IsNullOrEmpty(operation))
            {
                return await CreateErrorResponse(req, "Operation parameter required", correlationId);
            }

            return operation.ToLower() switch
            {
                "register" => await HandleUserRegistration(req, correlationId, cancellationToken),
                _ => await CreateErrorResponse(req, $"Invalid operation: {operation}", correlationId)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Registration service error [CorrelationId: {CorrelationId}]", correlationId);
            return await CreateErrorResponse(req, "Service error", correlationId);
        }
    }

    private async Task<HttpResponseData> HandleUserRegistration(HttpRequestData req, string correlationId, CancellationToken cancellationToken)
    {
        try
        {
            var invitationData = await JsonSerializer.DeserializeAsync<InvitationRequest>(req.Body, JsonOptions, cancellationToken);
            if (invitationData == null)
                return await CreateErrorResponse(req, "Invalid request data", correlationId);

            if (string.IsNullOrEmpty(invitationData.VerificationCode))
                return await CreateErrorResponse(req, "Invitation code required for registration", correlationId);

            var validationResults = new List<ValidationResult>();
            var validationContext = new ValidationContext(invitationData);
            if (!Validator.TryValidateObject(invitationData, validationContext, validationResults, true))
            {
                var errors = string.Join(", ", validationResults.Select(r => r.ErrorMessage));
                return await CreateErrorResponse(req, $"Validation failed: {errors}", correlationId);
            }

            var (isValid, tokenData, errorMessage) = string.IsNullOrEmpty(invitationData.Token)
                ? await _invitationTokenManager.ValidateInvitationByCode(invitationData.VerificationCode)
                : await _invitationTokenManager.ValidateInvitationToken(invitationData.Token, invitationData.VerificationCode);

            if (!isValid || tokenData == null)
                return await CreateErrorResponse(req, errorMessage ?? "Invalid invitation", correlationId);

            if (!string.Equals(tokenData.Email, invitationData.Email, StringComparison.OrdinalIgnoreCase))
                return await CreateErrorResponse(req, "Email address does not match invitation", correlationId);

            // user data
            var data = new AuthRequest
            {
                Email = invitationData.Email,
                Password = invitationData.Password ?? string.Empty,
                FirstName = invitationData.FirstName,
                LastName = invitationData.LastName,
                ApplicationName = tokenData.ApplicationId
            };

            var clientId = BaseFunctionService.GetClientIdentifier(req);
            var rateLimitInfo = await _rateLimitService.CheckRateLimitAsync(clientId, "register", cancellationToken);
            if (!rateLimitInfo.IsAllowed)
            {
                return await CreateJsonResponse(req, new
                {
                    success = false,
                    message = "Rate limit exceeded. Please try again later.",
                    errorCode = "RateLimitExceeded",
                    retryAfter = rateLimitInfo.WindowResetTime
                }, HttpStatusCode.TooManyRequests, correlationId);
            }

            // user exists?
            var existingUsers = await _graphServiceClient.Users
                .GetAsync(requestConfiguration =>
                {
                    var emailEsc = ODataHelpers.EscapeString(data.Email);
                    requestConfiguration.QueryParameters.Filter =
                        $"mail eq '{emailEsc}' or userPrincipalName eq '{emailEsc}' or proxyAddresses/any(c:c eq 'SMTP:{emailEsc}')";
                    requestConfiguration.QueryParameters.Select = new[] { "id", "mail", "userPrincipalName", "displayName" };
                    requestConfiguration.QueryParameters.Top = 1;
                }, cancellationToken);

            if (existingUsers?.Value?.Any() == true)
                return await CreateErrorResponse(req, "User already exists", correlationId);

            // validate password
            var passwordValidation = await _passwordHistoryService.ValidatePasswordAgainstHistoryAsync(
                data.ApplicationName ?? "Default Application", data.Email, data.Password, cancellationToken);

            if (!passwordValidation.IsSuccess)
                return await CreateErrorResponse(req, passwordValidation.ErrorMessage, correlationId);

            // create user
            var applicationName = data.ApplicationName ?? "Default Application";
            var upn = $"{Guid.NewGuid()}@{_entraOptions.DefaultDomain}";
            var displayNameWithContext = $"{data.FirstName} {data.LastName} ({applicationName})";

            var newUser = new User
            {
                DisplayName = displayNameWithContext,
                GivenName = data.FirstName,
                Surname = data.LastName,
                Mail = data.Email,
                UserPrincipalName = upn,
                Department = applicationName,
                Identities = new List<ObjectIdentity>
                {
                    new ObjectIdentity
                    {
                        SignInType = "emailAddress",
                        Issuer = _entraOptions.DefaultDomain,
                        IssuerAssignedId = data.Email
                    }
                },
                PasswordProfile = new PasswordProfile
                {
                    Password = data.Password,
                    ForceChangePasswordNextSignIn = false
                },
                AccountEnabled = true
            };

            var createdUser = await _graphServiceClient.Users.PostAsync(newUser, cancellationToken: cancellationToken);
            if (createdUser?.Id == null)
                return await CreateErrorResponse(req, "Failed to create user", correlationId);

            _logger.LogInformation("User created successfully: {UserId} for {Email} [CorrelationId: {CorrelationId}]",
                createdUser.Id, data.Email, correlationId);

            // Mark invitation token as used synchronously
            try
            {
                await _invitationTokenManager.MarkTokenAsUsed(invitationData.Token ?? string.Empty);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to mark invitation token as used for {Email} [CorrelationId: {CorrelationId}]", data.Email, correlationId);
            }

            _ = Task.Run(async () => await HandlePostCreationTasksAsync(data, invitationData, correlationId, cancellationToken));

            return await CreateJsonResponse(req, new { success = true, message = "User account created successfully" }, HttpStatusCode.OK, correlationId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Registration error [CorrelationId: {CorrelationId}]", correlationId);
            return await CreateErrorResponse(req, "Registration failed", correlationId);
        }
    }

    private async Task HandlePostCreationTasksAsync(AuthRequest userData, InvitationRequest invitationData, string correlationId, CancellationToken cancellationToken)
    {
        // Token marking moved to main handler for reliability

        // notification email
        try
        {
            await _emailService.SendAccountCreatedNotificationAsync(
                userData.Email, userData.FirstName ?? "User", userData.ApplicationName ?? "Default Application", correlationId, cancellationToken);
            _logger.LogInformation("Account created notification email sent to {Email} [CorrelationId: {CorrelationId}]", userData.Email, correlationId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending account created notification email to {Email} [CorrelationId: {CorrelationId}]", userData.Email, correlationId);
        }

        // update password history
        try
        {
            await _passwordHistoryService.UpdatePasswordHistoryAsync(
                userData.ApplicationName ?? "Default Application", userData.Email, userData.Password, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Password history error for {Email} [CorrelationId: {CorrelationId}]", userData.Email, correlationId);
        }
    }
}
